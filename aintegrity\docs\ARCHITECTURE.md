# Architecture Overview

AIntegrity is built as a modern full-stack SaaS application using Next.js with a focus on scalability, academic integrity, and enterprise-grade performance.

> **Note**: This architecture documentation is for authorized developers and technical stakeholders of the closed-source AIntegrity platform.

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (React)       │    │   (Next.js)     │    │   Services      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Chat UI       │◄──►│ • API Routes    │◄──►│ • OpenAI API    │
│ • Notes UI      │    │ • Auth Logic    │    │ • OAuth         │
│ • Auth UI       │    │ • DB Queries    │    │ • PostgreSQL    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Tech Stack Deep Dive

### Frontend Layer
- **Next.js 15** with App Router for routing and SSR
- **React 19** for UI components
- **Tailwind CSS** for styling
- **ShadCN/UI** for pre-built components
- **Zustand** for client-side state management

### Backend Layer
- **Next.js API Routes** for serverless functions
- **Prisma ORM** for database operations
- **NextAuth.js** for authentication
- **OpenAI SDK** for AI integration

### Database Layer
- **PostgreSQL** as primary database
- **pgvector** extension for vector embeddings
- **Prisma** for schema management and migrations

### External Services
- **OpenAI** for chat completions and embeddings
- **Google/GitHub** for OAuth authentication
- **Vercel** for deployment and hosting

## Data Flow

### Chat Flow
1. User sends message via chat interface
2. Frontend calls `/api/chat` endpoint
3. Backend retrieves user context from database
4. System generates embeddings for message
5. Relevant notes are found using vector similarity
6. Context + message sent to OpenAI API
7. AI response returned and saved to database
8. Response streamed back to frontend

### Notes Flow
1. User creates/edits note via notes interface
2. Frontend calls `/api/notes` endpoints
3. Backend generates embeddings for note content
4. Note and embeddings saved to database
5. Note becomes searchable in future chats

### Authentication Flow
1. User clicks OAuth provider (Google/GitHub)
2. Redirected to provider for authentication
3. Provider redirects back with authorization code
4. NextAuth.js exchanges code for user info
5. User session created and stored
6. User redirected to dashboard

## Database Schema

### Core Tables

**Users**
- Stores user profile information
- Links to OAuth accounts and sessions

**Notes**
- User's knowledge base entries
- Includes vector embeddings for search
- Supports tags and public/private visibility

**Chats & Messages**
- Conversation history
- Links messages to specific chats
- Stores both user and AI messages

**Auth Tables**
- NextAuth.js standard tables
- Accounts, Sessions, VerificationTokens

### Key Relationships
- User → Notes (one-to-many)
- User → Chats (one-to-many)
- Chat → Messages (one-to-many)
- User → Accounts (one-to-many)

## API Design

### RESTful Endpoints

**Authentication**
- `GET/POST /api/auth/*` - NextAuth.js handlers

**Chat**
- `POST /api/chat` - Send message and get AI response

**Notes**
- `GET /api/notes` - List user's notes
- `POST /api/notes` - Create new note
- `GET /api/notes/[id]` - Get specific note
- `PUT /api/notes/[id]` - Update note
- `DELETE /api/notes/[id]` - Delete note

### Request/Response Format
All APIs use JSON for request/response bodies with consistent error handling.

## Security Considerations

### Authentication
- JWT-based sessions via NextAuth.js
- OAuth providers for secure login
- Session validation on all protected routes

### Authorization
- User-scoped data access
- API routes validate user ownership
- Private notes are user-isolated

### Data Protection
- Environment variables for secrets
- API keys stored securely
- Database connections encrypted

## Performance Optimizations

### Frontend
- React Server Components for faster initial loads
- Client-side caching with React Query patterns
- Optimistic updates for better UX

### Backend
- Database connection pooling via Prisma
- Efficient queries with proper indexing
- Vector similarity search for relevant context

### Deployment
- Vercel Edge Functions for global distribution
- CDN for static assets
- Database hosted close to application

## Scalability Considerations

### Horizontal Scaling
- Stateless API design
- Database can be scaled independently
- Serverless functions auto-scale

### Vertical Scaling
- Efficient database queries
- Proper indexing strategy
- Connection pooling

### Future Enhancements
- Redis for caching frequently accessed data
- Separate vector database (Pinecone/Weaviate)
- Background job processing for embeddings
- Real-time features with WebSockets

## Development Workflow

### Local Development
1. Clone repository
2. Install dependencies
3. Set up environment variables
4. Run database migrations
5. Start development server

### Code Organization
- Feature-based component structure
- Shared utilities in `/lib`
- Type definitions in `/types`
- API routes in `/app/api`

### Testing Strategy
- Unit tests for utility functions
- Integration tests for API routes
- E2E tests for critical user flows
- Database tests with test containers
