import OpenAI from 'openai'
import { getTutoringSystemPrompt } from './tutoring-prompts'

if (!process.env.OPENAI_API_KEY) {
  throw new Error('Missing OPENAI_API_KEY environment variable')
}

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function generateEmbedding(text: string): Promise<number[]> {
  const response = await openai.embeddings.create({
    model: 'text-embedding-3-small',
    input: text,
  })
  
  return response.data[0].embedding
}

export async function generateChatCompletion(
  messages: Array<{ role: 'user' | 'assistant' | 'system'; content: string }>,
  context?: string
) {
  // Use the comprehensive tutoring system prompt that includes:
  // - Academic integrity rules
  // - Mermaid diagram capabilities
  // - Note context integration
  // - Source citation requirements
  const systemMessage = {
    role: 'system' as const,
    content: getTutoringSystemPrompt(context)
  }

  // Generate the AI response with tutoring guidelines
  const response = await openai.chat.completions.create({
    model: 'gpt-4.1-mini',
    messages: [systemMessage, ...messages],
    temperature: 0.7,
    max_tokens: 2000,
  })

  return response.choices[0]?.message?.content || ''
}
