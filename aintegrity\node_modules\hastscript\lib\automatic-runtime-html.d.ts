/* eslint-disable @typescript-eslint/naming-convention */

import type {Element, Root} from 'hast'
import type {Child} from './create-h.js'
import type {JSXProps} from './create-automatic-runtime.js'

export * from './jsx-automatic.js'

export const Fragment: null

export const jsxDEV: {
  (
    type: null,
    properties: {children?: Child},
    key?: string | null | undefined
  ): Root
  (type: string, properties: JSXProps, key?: string | null | undefined): Element
}

export const jsxs: {
  (
    type: null,
    properties: {children?: Child},
    key?: string | null | undefined
  ): Root
  (type: string, properties: JSXProps, key?: string | null | undefined): Element
}

export const jsx: {
  (
    type: null,
    properties: {children?: Child},
    key?: string | null | undefined
  ): Root
  (type: string, properties: JSXProps, key?: string | null | undefined): Element
}
