/**
 * Create an extension for `micromark` to enable math syntax.
 *
 * @param {Options | null | undefined} [options={}]
 *   Configuration (default: `{}`).
 * @returns {Extension}
 *   Extension for `micromark` that can be passed in `extensions`, to
 *   enable math syntax.
 */
export function math(options?: Options | null | undefined): Extension;
import type { Options } from 'micromark-extension-math';
import type { Extension } from 'micromark-util-types';
