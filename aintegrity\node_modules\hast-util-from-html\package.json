{"name": "hast-util-from-html", "version": "2.0.3", "description": "hast utility to parse from HTML", "license": "MIT", "keywords": ["unist", "hast", "hast-util", "util", "utility", "html", "parse", "tokenize"], "repository": "syntax-tree/hast-util-from-html", "bugs": "https://github.com/syntax-tree/hast-util-from-html/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/hast": "^3.0.0", "devlop": "^1.1.0", "hast-util-from-parse5": "^8.0.0", "parse5": "^7.0.0", "vfile": "^6.0.0", "vfile-message": "^4.0.0"}, "devDependencies": {"@types/mdast": "^4.0.0", "@types/node": "^22.0.0", "c8": "^10.0.0", "mdast-zone": "^6.0.0", "prettier": "^3.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "to-vfile": "^8.0.0", "type-coverage": "^2.0.0", "type-fest": "^4.0.0", "typescript": "^5.0.0", "unist-builder": "^4.0.0", "xo": "^0.59.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["preset-wooorm", ["remark-lint-no-html", false], "./script/parse-error.js"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}, {"files": "test/**/*.js", "rules": {"no-await-in-loop": "off"}}], "prettier": true, "rules": {"unicorn/prefer-code-point": "off", "unicorn/prefer-string-replace-all": "off"}}}