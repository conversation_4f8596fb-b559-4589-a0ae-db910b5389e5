# Setup Guide

Complete setup instructions for AIntegrity - AI-Powered Academic Tutoring Platform.

> **Note**: AIntegrity is a closed-source SaaS application. This setup guide is for development and deployment purposes for authorized users.

## Prerequisites

- Node.js 18+ and npm
- PostgreSQL database (local or hosted)
- OpenAI API key
- Google/GitHub OAuth credentials (optional)

## 1. Environment Setup

### Copy Environment File
```bash
cp .env.example .env
```

### Configure Environment Variables

Edit `.env` with your actual values:

```env
# Database - Choose one option:

# Option A: Local PostgreSQL
DATABASE_URL="postgresql://username:password@localhost:5432/aintegrity?schema=public"

# Option B: Supabase (recommended for beginners)
DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"  # Generate with: openssl rand -base64 32

# OpenAI (required)
OPENAI_API_KEY="sk-..."  # Get from https://platform.openai.com/api-keys

# OAuth Providers (optional but recommended)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

## 2. Database Setup

### Option A: Local PostgreSQL

1. **Install PostgreSQL**
   ```bash
   # macOS
   brew install postgresql
   brew services start postgresql
   
   # Ubuntu/Debian
   sudo apt update
   sudo apt install postgresql postgresql-contrib
   sudo systemctl start postgresql
   
   # Windows
   # Download from https://www.postgresql.org/download/windows/
   ```

2. **Create Database**
   ```bash
   createdb aintegrity
   ```

3. **Install pgvector (for embeddings)**
   ```sql
   -- Connect to your database and run:
   CREATE EXTENSION IF NOT EXISTS vector;
   ```

### Option B: Supabase (Recommended)

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Wait for setup to complete

2. **Get Connection String**
   - Go to Settings > Database
   - Copy the connection string
   - Replace `[password]` with your database password

3. **Enable pgvector**
   - Go to SQL Editor in Supabase
   - Run: `CREATE EXTENSION IF NOT EXISTS vector;`

## 3. OAuth Setup (Optional)

### Google OAuth

1. **Google Cloud Console**
   - Go to [console.cloud.google.com](https://console.cloud.google.com)
   - Create new project or select existing
   - Enable Google+ API

2. **Create OAuth Credentials**
   - Go to Credentials > Create Credentials > OAuth 2.0 Client ID
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`

3. **Copy Credentials**
   - Copy Client ID and Client Secret to `.env`

### GitHub OAuth

1. **GitHub Settings**
   - Go to GitHub Settings > Developer settings > OAuth Apps
   - Click "New OAuth App"

2. **App Configuration**
   - Application name: AIntegrity
   - Homepage URL: `http://localhost:3000`
   - Authorization callback URL: `http://localhost:3000/api/auth/callback/github`

3. **Copy Credentials**
   - Copy Client ID and Client Secret to `.env`

## 4. Install Dependencies

```bash
npm install
```

## 5. Database Migration

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push
```

## 6. Start Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` to see your application!

## 7. Verify Setup

1. **Database Connection**: Check that tables are created in your database
2. **Authentication**: Try signing in with Google/GitHub
3. **OpenAI Integration**: Create a note and try chatting
4. **Note Creation**: Create and save a note

## Troubleshooting

### Common Issues

**Database Connection Error**
- Verify DATABASE_URL is correct
- Check if PostgreSQL is running
- Ensure database exists

**OAuth Not Working**
- Check redirect URIs match exactly
- Verify client ID/secret are correct
- Ensure OAuth app is not in development mode (for production)

**OpenAI API Error**
- Verify API key is correct
- Check if you have credits in your OpenAI account
- Ensure API key has proper permissions

**Build Errors**
- Clear node_modules and reinstall: `rm -rf node_modules package-lock.json && npm install`
- Check Node.js version (18+ required)

### Getting Help

- Check the [Development Guide](./DEVELOPMENT.md)
- Review [API Documentation](./API.md)
- Contact support for technical assistance
- Join the user community for tips and best practices
