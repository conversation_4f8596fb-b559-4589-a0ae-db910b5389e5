export namespace errors {
    let abandonedHeadElementChild: ErrorInfo;
    let abruptClosingOfEmptyComment: ErrorInfo;
    let abruptDoctypePublicIdentifier: ErrorInfo;
    let abruptDoctypeSystemIdentifier: ErrorInfo;
    let absenceOfDigitsInNumericCharacterReference: ErrorInfo;
    let cdataInHtmlContent: ErrorInfo;
    let characterReferenceOutsideUnicodeRange: ErrorInfo;
    let closingOfElementWithOpenChildElements: ErrorInfo;
    let controlCharacterInInputStream: ErrorInfo;
    let controlCharacterReference: ErrorInfo;
    let disallowedContentInNoscriptInHead: ErrorInfo;
    let duplicateAttribute: ErrorInfo;
    let endTagWithAttributes: ErrorInfo;
    let endTagWithTrailingSolidus: ErrorInfo;
    let endTagWithoutMatchingOpenElement: ErrorInfo;
    let eofBeforeTagName: ErrorInfo;
    let eofInCdata: ErrorInfo;
    let eofInComment: ErrorInfo;
    let eofInDoctype: ErrorInfo;
    let eofInElementThatCanContainOnlyText: ErrorInfo;
    let eofInScriptHtmlCommentLikeText: ErrorInfo;
    let eofInTag: ErrorInfo;
    let incorrectlyClosedComment: ErrorInfo;
    let incorrectlyOpenedComment: ErrorInfo;
    let invalidCharacterSequenceAfterDoctypeName: ErrorInfo;
    let invalidFirstCharacterOfTagName: ErrorInfo;
    let misplacedDoctype: ErrorInfo;
    let misplacedStartTagForHeadElement: ErrorInfo;
    let missingAttributeValue: ErrorInfo;
    let missingDoctype: ErrorInfo;
    let missingDoctypeName: ErrorInfo;
    let missingDoctypePublicIdentifier: ErrorInfo;
    let missingDoctypeSystemIdentifier: ErrorInfo;
    let missingEndTagName: ErrorInfo;
    let missingQuoteBeforeDoctypePublicIdentifier: ErrorInfo;
    let missingQuoteBeforeDoctypeSystemIdentifier: ErrorInfo;
    let missingSemicolonAfterCharacterReference: ErrorInfo;
    let missingWhitespaceAfterDoctypePublicKeyword: ErrorInfo;
    let missingWhitespaceAfterDoctypeSystemKeyword: ErrorInfo;
    let missingWhitespaceBeforeDoctypeName: ErrorInfo;
    let missingWhitespaceBetweenAttributes: ErrorInfo;
    let missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers: ErrorInfo;
    let nestedComment: ErrorInfo;
    let nestedNoscriptInHead: ErrorInfo;
    let nonConformingDoctype: ErrorInfo;
    let nonVoidHtmlElementStartTagWithTrailingSolidus: ErrorInfo;
    let noncharacterCharacterReference: ErrorInfo;
    let noncharacterInInputStream: ErrorInfo;
    let nullCharacterReference: ErrorInfo;
    let openElementsLeftAfterEof: ErrorInfo;
    let surrogateCharacterReference: ErrorInfo;
    let surrogateInInputStream: ErrorInfo;
    let unexpectedCharacterAfterDoctypeSystemIdentifier: ErrorInfo;
    let unexpectedCharacterInAttributeName: ErrorInfo;
    let unexpectedCharacterInUnquotedAttributeValue: ErrorInfo;
    let unexpectedEqualsSignBeforeAttributeName: ErrorInfo;
    let unexpectedNullCharacter: ErrorInfo;
    let unexpectedQuestionMarkInsteadOfTagName: ErrorInfo;
    let unexpectedSolidusInTag: ErrorInfo;
    let unknownNamedCharacterReference: ErrorInfo;
}
/**
 * Info on a `parse5` error.
 */
export type ErrorInfo = {
    /**
     *   Reason of error.
     */
    reason: string;
    /**
     *   More info on error.
     */
    description: string;
    /**
     * Turn off if this is not documented in the html5 spec (optional).
     */
    url?: false;
};
//# sourceMappingURL=errors.d.ts.map