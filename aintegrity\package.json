{"name": "aintegrity", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npx prisma generate && next build", "start": "next start", "lint": "next lint", "db:generate": "npx prisma generate", "db:push": "npx prisma db push", "db:migrate": "npx prisma migrate dev", "db:studio": "npx prisma studio", "db:seed": "tsx prisma/seed.ts", "postinstall": "npx prisma generate", "check-env": "node scripts/check-env.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@prisma/client": "^6.9.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.50.0", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.515.0", "mermaid": "^11.4.0", "next": "15.3.3", "next-auth": "^4.24.11", "openai": "^5.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.9.0", "tailwindcss": "^4", "typescript": "^5"}}