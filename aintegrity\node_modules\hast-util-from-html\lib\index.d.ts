/**
 * Turn serialized HTML into a hast tree.
 *
 * @param {VFile | Value} value
 *   Serialized HTML to parse.
 * @param {Readonly<Options> | null | undefined} [options]
 *   Configuration (optional).
 * @returns {Root}
 *   Tree.
 */
export function fromHtml(value: VFile | Value, options?: Readonly<Options> | null | undefined): Root;
import { VFile } from 'vfile';
import type { Value } from 'vfile';
import type { Options } from './types.js';
import type { Root } from 'hast';
//# sourceMappingURL=index.d.ts.map