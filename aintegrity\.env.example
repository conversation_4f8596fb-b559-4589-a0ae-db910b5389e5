# AIntegrity Environment Configuration
# Copy this file to .env and fill in your actual values
# See docs/SETUP.md for detailed configuration instructions

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Choose one database option below:

# Option A: Local PostgreSQL
DATABASE_URL="postgresql://username:password@localhost:5432/aintegrity?schema=public"

# Option B: Supabase (recommended for beginners)
# DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres?schema=public"

# Option C: Neon Database (recommended for production)
# DATABASE_URL="postgresql://[user]:[password]@[host]/[database]?sslmode=require"

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3000"  # Change to your domain in production
NEXTAUTH_SECRET="your-secret-key-here"  # Generate with: openssl rand -base64 32

# =============================================================================
# AI INTEGRATION
# =============================================================================

# OpenAI Configuration (Required)
OPENAI_API_KEY="your-openai-api-key-here"  # Get from https://platform.openai.com/api-keys

# =============================================================================
# OAUTH PROVIDERS (Optional but recommended)
# =============================================================================

# Google OAuth
# Get credentials from: https://console.cloud.google.com
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# GitHub OAuth
# Get credentials from: https://github.com/settings/developers
GITHUB_ID=""
GITHUB_SECRET=""

# =============================================================================
# SUPABASE CONFIGURATION (If using Supabase)
# =============================================================================

# Supabase Configuration (alternative to local PostgreSQL)
# Get these from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL=""
NEXT_PUBLIC_SUPABASE_ANON_KEY=""
SUPABASE_SERVICE_ROLE_KEY=""

# =============================================================================
# PRODUCTION ENVIRONMENT VARIABLES
# =============================================================================

# Uncomment and configure for production deployment
# NODE_ENV="production"
# NEXTAUTH_URL="https://your-domain.com"
# VERCEL_URL="your-vercel-url"  # Automatically set by Vercel
