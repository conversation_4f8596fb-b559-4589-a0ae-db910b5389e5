# web-namespaces

[![Build][build-badge]][build]
[![Coverage][coverage-badge]][coverage]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

Map of web namespaces.

## Contents

*   [What is this?](#what-is-this)
*   [When should I use this?](#when-should-i-use-this)
*   [Install](#install)
*   [Use](#use)
*   [API](#api)
    *   [`webNamespaces`](#webnamespaces)
*   [Types](#types)
*   [Compatibility](#compatibility)
*   [Security](#security)
*   [Related](#related)
*   [Contribute](#contribute)
*   [License](#license)

## What is this?

This is a map of names to namespaces found on the web platforms.

## When should I use this?

You can use this package if you want to access the XML, HTML, SVG, etc.
namespace urls.

## Install

This package is [ESM only][esm].
In Node.js (version 12.20+, 14.14+, or 16.0+), install with [npm][]:

```sh
npm install web-namespaces
```

In Deno with [Skypack][]:

```js
import {webNamespaces} from 'https://cdn.skypack.dev/web-namespaces@2?dts'
```

In browsers with [Skypack][]:

```html
<script type="module">
  import {webNamespaces} from 'https://cdn.skypack.dev/web-namespaces@2?min'
</script>
```

## Use

```js
import {webNamespaces} from 'web-namespaces'

console.log(webNamespaces)
```

Yields:

```js
{
  html: 'http://www.w3.org/1999/xhtml',
  mathml: 'http://www.w3.org/1998/Math/MathML',
  svg: 'http://www.w3.org/2000/svg',
  xlink: 'http://www.w3.org/1999/xlink',
  xml: 'http://www.w3.org/XML/1998/namespace',
  xmlns: 'http://www.w3.org/2000/xmlns/'
}
```

## API

This package exports the following identifiers: `webNamespaces`.
There is no default export.

### `webNamespaces`

Map of names to namespaces (`Record<string, string>`).

## Types

This package is fully typed with [TypeScript][].
There are no extra exported types.

## Compatibility

This package is at least compatible with all maintained versions of Node.js.
As of now, that is Node.js 12.20+, 14.14+, and 16.0+.
It also works in Deno and modern browsers.

## Security

This package is safe.

## Related

*   [`wooorm/html-tag-names`](https://github.com/wooorm/html-tag-names)
    — list of HTML tag names
*   [`wooorm/mathml-tag-names`](https://github.com/wooorm/mathml-tag-names)
    — list of MathML tag names
*   [`wooorm/svg-tag-names`](https://github.com/wooorm/svg-tag-names)
    — list of SVG tag names
*   [`wooorm/svg-element-attributes`](https://github.com/wooorm/svg-element-attributes)
    — map of SVG elements to allowed attributes
*   [`wooorm/html-element-attributes`](https://github.com/wooorm/html-element-attributes)
    — map of HTML elements to allowed attributes
*   [`wooorm/aria-attributes`](https://github.com/wooorm/aria-attributes)
    — list of ARIA attributes

## Contribute

Yes please!
See [How to Contribute to Open Source][contribute].

## License

[MIT][license] © [Titus Wormer][author]

<!-- Definition -->

[build-badge]: https://github.com/wooorm/web-namespaces/workflows/main/badge.svg

[build]: https://github.com/wooorm/web-namespaces/actions

[coverage-badge]: https://img.shields.io/codecov/c/github/wooorm/web-namespaces.svg

[coverage]: https://codecov.io/github/wooorm/web-namespaces

[downloads-badge]: https://img.shields.io/npm/dm/web-namespaces.svg

[downloads]: https://www.npmjs.com/package/web-namespaces

[size-badge]: https://img.shields.io/bundlephobia/minzip/web-namespaces.svg

[size]: https://bundlephobia.com/result?p=web-namespaces

[npm]: https://docs.npmjs.com/cli/install

[skypack]: https://www.skypack.dev

[license]: license

[author]: https://wooorm.com

[esm]: https://gist.github.com/sindresorhus/a39789f98801d908bbc7ff3ecc99d99c

[typescript]: https://www.typescriptlang.org

[contribute]: https://opensource.guide/how-to-contribute/
