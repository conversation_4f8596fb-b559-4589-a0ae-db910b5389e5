/** @type {ReturnType<createH>} */
export const h: ReturnType<typeof createH>;
export namespace h {
    namespace JSX {
        type Element = import("./jsx-classic.js").Element;
        type ElementChildrenAttribute = import("./jsx-classic.js").ElementChildrenAttribute;
        type IntrinsicAttributes = import("./jsx-classic.js").IntrinsicAttributes;
        type IntrinsicElements = import("./jsx-classic.js").IntrinsicElements;
    }
}
/** @type {ReturnType<createH>} */
export const s: ReturnType<typeof createH>;
export namespace s {
    namespace JSX {
        type Element = import("./jsx-classic.js").Element;
        type ElementChildrenAttribute = import("./jsx-classic.js").ElementChildrenAttribute;
        type IntrinsicAttributes = import("./jsx-classic.js").IntrinsicAttributes;
        type IntrinsicElements = import("./jsx-classic.js").IntrinsicElements;
    }
}
import { createH } from './create-h.js';
//# sourceMappingURL=index.d.ts.map