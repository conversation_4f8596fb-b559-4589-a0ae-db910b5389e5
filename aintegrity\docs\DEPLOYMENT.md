# Deployment Guide

Production deployment instructions for AIntegrity - AI-Powered Academic Tutoring Platform.

> **Note**: AIntegrity is a closed-source SaaS application. This deployment guide is for authorized deployments and enterprise installations.

## Deployment Options

### Option 1: Vercel (Recommended)

Vercel provides the best experience for Next.js applications with automatic deployments and scaling.

#### Prerequisites
- Vercel account
- GitHub repository
- Production database (Supabase/Neon recommended)
- OpenAI API key

#### Steps

1. **Prepare Repository**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Configure project settings

3. **Environment Variables**
   Add these in Vercel dashboard under Settings > Environment Variables:
   ```
   DATABASE_URL=your-production-database-url
   NEXTAUTH_URL=https://your-domain.vercel.app
   NEXTAUTH_SECRET=your-production-secret
   OPENAI_API_KEY=your-openai-key
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   GITHUB_ID=your-github-client-id
   GITHUB_SECRET=your-github-client-secret
   ```

4. **Deploy**
   - Click "Deploy"
   - Wait for build to complete
   - Visit your deployed application

5. **Set up Database**
   ```bash
   # Run migrations on production database
   npx prisma db push
   ```

#### Custom Domain (Optional)
1. Go to Vercel project settings
2. Add your custom domain
3. Configure DNS records as instructed
4. Update NEXTAUTH_URL environment variable

### Option 2: Railway

Railway is another excellent option for full-stack applications.

#### Steps

1. **Create Railway Account**
   - Go to [railway.app](https://railway.app)
   - Connect GitHub account

2. **Deploy from GitHub**
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your repository

3. **Add Database**
   - Click "New" > "Database" > "PostgreSQL"
   - Note the connection details

4. **Environment Variables**
   Add in Railway dashboard:
   ```
   DATABASE_URL=${{Postgres.DATABASE_URL}}
   NEXTAUTH_URL=${{RAILWAY_STATIC_URL}}
   NEXTAUTH_SECRET=your-secret
   OPENAI_API_KEY=your-openai-key
   # ... other OAuth variables
   ```

5. **Deploy**
   Railway will automatically deploy on git push

### Option 3: Self-Hosted (VPS)

For more control, deploy on your own server.

#### Prerequisites
- Ubuntu/Debian VPS
- Domain name
- SSL certificate (Let's Encrypt)

#### Steps

1. **Server Setup**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PostgreSQL
   sudo apt install postgresql postgresql-contrib
   
   # Install Nginx
   sudo apt install nginx
   
   # Install PM2
   sudo npm install -g pm2
   ```

2. **Database Setup**
   ```bash
   sudo -u postgres createuser --interactive
   sudo -u postgres createdb aintegrity
   sudo -u postgres psql -c "ALTER USER username PASSWORD 'password';"
   ```

3. **Application Setup**
   ```bash
   # Clone repository
   git clone https://github.com/yourusername/aintegrity.git
   cd aintegrity
   
   # Install dependencies
   npm install
   
   # Set up environment
   cp .env.example .env
   # Edit .env with production values
   
   # Build application
   npm run build
   
   # Run migrations
   npx prisma db push
   ```

4. **Process Management**
   ```bash
   # Start with PM2
   pm2 start npm --name "aintegrity" -- start
   pm2 save
   pm2 startup
   ```

5. **Nginx Configuration**
   ```nginx
   # /etc/nginx/sites-available/aintegrity
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

6. **SSL Certificate**
   ```bash
   sudo apt install certbot python3-certbot-nginx
   sudo certbot --nginx -d your-domain.com
   ```

## Database Setup

### Supabase (Recommended)

1. **Create Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Wait for setup completion

2. **Get Connection String**
   - Go to Settings > Database
   - Copy connection string
   - Replace password placeholder

3. **Enable Extensions**
   ```sql
   -- In Supabase SQL Editor
   CREATE EXTENSION IF NOT EXISTS vector;
   ```

4. **Run Migrations**
   ```bash
   DATABASE_URL="your-supabase-url" npx prisma db push
   ```

### Neon Database

1. **Create Account**
   - Go to [neon.tech](https://neon.tech)
   - Create new project

2. **Get Connection Details**
   - Copy connection string from dashboard
   - Note: Neon includes pgvector by default

3. **Run Migrations**
   ```bash
   DATABASE_URL="your-neon-url" npx prisma db push
   ```

## Environment Variables

### Production Environment Variables

```bash
# Database
DATABASE_URL="********************************/db?sslmode=require"

# NextAuth
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-32-char-secret"  # Generate: openssl rand -base64 32

# OpenAI
OPENAI_API_KEY="sk-your-openai-key"

# OAuth (Production Apps)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_ID="your-github-client-id"
GITHUB_SECRET="your-github-client-secret"
```

### Security Notes
- Use different OAuth apps for production
- Generate new NEXTAUTH_SECRET for production
- Use environment-specific database
- Enable SSL for database connections

## OAuth Configuration

### Google OAuth (Production)

1. **Google Cloud Console**
   - Create production OAuth client
   - Add production domain to authorized origins
   - Add `https://your-domain.com/api/auth/callback/google` to redirect URIs

2. **Verification**
   - Submit app for verification if needed
   - Add privacy policy and terms of service

### GitHub OAuth (Production)

1. **GitHub Settings**
   - Create new OAuth app for production
   - Set homepage URL to your domain
   - Set callback URL to `https://your-domain.com/api/auth/callback/github`

2. **App Settings**
   - Upload app logo
   - Add description
   - Set application name

## Monitoring and Maintenance

### Health Checks

Create health check endpoint:
```typescript
// src/app/api/health/route.ts
import { NextResponse } from 'next/server'
import { prisma } from '@/lib/db'

export async function GET() {
  try {
    await prisma.$queryRaw`SELECT 1`
    return NextResponse.json({ status: 'healthy' })
  } catch (error) {
    return NextResponse.json(
      { status: 'unhealthy', error: error.message },
      { status: 500 }
    )
  }
}
```

### Logging

Set up structured logging:
```typescript
// src/lib/logger.ts
export const logger = {
  info: (message: string, meta?: any) => {
    console.log(JSON.stringify({ level: 'info', message, meta, timestamp: new Date() }))
  },
  error: (message: string, error?: any) => {
    console.error(JSON.stringify({ level: 'error', message, error: error?.message, timestamp: new Date() }))
  }
}
```

### Performance Monitoring

Consider adding:
- Sentry for error tracking
- Vercel Analytics for performance
- Database query monitoring
- API response time tracking

## Backup Strategy

### Database Backups

**Supabase:**
- Automatic daily backups included
- Point-in-time recovery available
- Manual backups via dashboard

**Self-hosted:**
```bash
# Daily backup script
#!/bin/bash
pg_dump -h localhost -U username -d aintegrity > backup_$(date +%Y%m%d).sql
```

### Application Backups
- Code is backed up in Git repository
- Environment variables documented
- Database schema in Prisma files

## Scaling Considerations

### Horizontal Scaling
- Vercel automatically scales serverless functions
- Database connection pooling via Prisma
- CDN for static assets

### Performance Optimization
- Enable Next.js caching
- Optimize database queries
- Use Redis for session storage (future)
- Implement rate limiting

### Cost Optimization
- Monitor Vercel usage
- Optimize database queries
- Use appropriate instance sizes
- Set up billing alerts

## Troubleshooting

### Common Issues

**Build Failures:**
- Check environment variables
- Verify database connectivity
- Review build logs

**Database Connection:**
- Verify connection string
- Check SSL requirements
- Ensure database is accessible

**OAuth Issues:**
- Verify redirect URIs
- Check client ID/secret
- Ensure apps are published

**Performance Issues:**
- Monitor database queries
- Check API response times
- Review error logs

### Getting Help
- Check Vercel documentation
- Review Next.js deployment guides
- Monitor application logs
- Use database monitoring tools
