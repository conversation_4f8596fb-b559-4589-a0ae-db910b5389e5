# AIntegrity Documentation

Welcome to the comprehensive documentation for AIntegrity - AI-Powered Academic Tutoring Platform.

## 📚 Documentation Index

### Getting Started
- **[Setup Guide](./SETUP.md)** - Complete installation and configuration instructions
- **[Quick Start](#quick-start)** - Get up and running in minutes
- **[Environment Configuration](#environment-configuration)** - Setting up your development environment

### Development
- **[Development Guide](./DEVELOPMENT.md)** - Development workflow, coding standards, and best practices
- **[Architecture Overview](./ARCHITECTURE.md)** - System design, data flow, and technical decisions
- **[API Reference](./API.md)** - Complete API documentation with examples

### Features & Deployment
- **[Features Overview](./FEATURES.md)** - Current features and development roadmap
- **[Deployment Guide](./DEPLOYMENT.md)** - Production deployment instructions for various platforms

## 🚀 Quick Start

1. **Prerequisites**
   - Node.js 18+ and npm
   - PostgreSQL database (local or hosted)
   - OpenAI API key

2. **Installation**
   ```bash
   git clone <repository-url>
   cd AIntegrity/aintegrity
   npm install
   ```

3. **Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Database Setup**
   ```bash
   npm run db:generate
   npm run db:push
   ```

5. **Start Development**
   ```bash
   npm run dev
   ```

For detailed instructions, see the [Setup Guide](./SETUP.md).

## 🔧 Environment Configuration

AIntegrity requires several environment variables to function properly:

### Required Variables
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - Secret for session encryption
- `OPENAI_API_KEY` - OpenAI API key for AI functionality

### Optional Variables
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET` - Google OAuth
- `GITHUB_ID` & `GITHUB_SECRET` - GitHub OAuth
- `NEXTAUTH_URL` - Base URL for authentication (auto-detected in development)

See the [Setup Guide](./SETUP.md) for detailed configuration instructions.

## 🏗️ Architecture Overview

AIntegrity is built as a modern full-stack SaaS application:

- **Frontend**: Next.js 15 with React 19, Tailwind CSS, ShadCN/UI
- **Backend**: Next.js API Routes, Prisma ORM, NextAuth.js
- **Database**: PostgreSQL with pgvector for embeddings
- **AI Integration**: OpenAI GPT-4o-mini for tutoring, text-embedding-3-small for search
- **Deployment**: Vercel with serverless functions

For detailed architecture information, see the [Architecture Overview](./ARCHITECTURE.md).

## 🎯 Key Features

### Academic Integrity Focus
- Never provides direct answers to homework or assignments
- Guides learning through step-by-step problem-solving
- Cannot be circumvented through clever prompting
- Maintains ethical academic standards

### Intelligent Tutoring
- Context-aware responses using personal knowledge base
- Automatic Mermaid diagram generation for visual learning
- Source citation for all educational content
- Personalized tutoring based on learning progress

### Knowledge Management
- Rich note-taking with semantic search
- Tag-based organization and categorization
- Vector embeddings for intelligent content discovery
- Public/private note sharing capabilities

For complete feature information, see the [Features Overview](./FEATURES.md).

## 🔗 Useful Links

### Development Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [NextAuth.js Documentation](https://next-auth.js.org)
- [OpenAI API Documentation](https://platform.openai.com/docs)

### Deployment Platforms
- [Vercel](https://vercel.com) - Recommended for Next.js applications
- [Supabase](https://supabase.com) - Managed PostgreSQL with pgvector
- [Neon](https://neon.tech) - Serverless PostgreSQL

### Design Resources
- [Tailwind CSS](https://tailwindcss.com)
- [ShadCN/UI](https://ui.shadcn.com)
- [Lucide Icons](https://lucide.dev)

## 📄 License

AIntegrity is a closed-source commercial application. All rights reserved.

---

For questions or support, please refer to the appropriate documentation section or contact the development team.
