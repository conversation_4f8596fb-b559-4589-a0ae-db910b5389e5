/** All valid namespaces in HTML. */
export declare enum NS {
    HTML = "http://www.w3.org/1999/xhtml",
    MATHML = "http://www.w3.org/1998/Math/MathML",
    SVG = "http://www.w3.org/2000/svg",
    XLINK = "http://www.w3.org/1999/xlink",
    XML = "http://www.w3.org/XML/1998/namespace",
    XMLNS = "http://www.w3.org/2000/xmlns/"
}
export declare enum ATTRS {
    TYPE = "type",
    ACTION = "action",
    ENCODING = "encoding",
    PROMPT = "prompt",
    NAME = "name",
    COLOR = "color",
    FACE = "face",
    SIZE = "size"
}
/**
 * The mode of the document.
 *
 * @see {@link https://dom.spec.whatwg.org/#concept-document-limited-quirks}
 */
export declare enum DOCUMENT_MODE {
    NO_QUIRKS = "no-quirks",
    QUIRKS = "quirks",
    LIMITED_QUIRKS = "limited-quirks"
}
export declare enum TAG_NAMES {
    A = "a",
    ADDRESS = "address",
    ANNOTATION_XML = "annotation-xml",
    APPLET = "applet",
    AREA = "area",
    ARTICLE = "article",
    ASIDE = "aside",
    B = "b",
    BASE = "base",
    BASEFONT = "basefont",
    BGSOUND = "bgsound",
    BIG = "big",
    BLOCKQUOTE = "blockquote",
    BODY = "body",
    BR = "br",
    BUTTON = "button",
    CAPTION = "caption",
    CENTER = "center",
    CODE = "code",
    COL = "col",
    COLGROUP = "colgroup",
    DD = "dd",
    DESC = "desc",
    DETAILS = "details",
    DIALOG = "dialog",
    DIR = "dir",
    DIV = "div",
    DL = "dl",
    DT = "dt",
    EM = "em",
    EMBED = "embed",
    FIELDSET = "fieldset",
    FIGCAPTION = "figcaption",
    FIGURE = "figure",
    FONT = "font",
    FOOTER = "footer",
    FOREIGN_OBJECT = "foreignObject",
    FORM = "form",
    FRAME = "frame",
    FRAMESET = "frameset",
    H1 = "h1",
    H2 = "h2",
    H3 = "h3",
    H4 = "h4",
    H5 = "h5",
    H6 = "h6",
    HEAD = "head",
    HEADER = "header",
    HGROUP = "hgroup",
    HR = "hr",
    HTML = "html",
    I = "i",
    IMG = "img",
    IMAGE = "image",
    INPUT = "input",
    IFRAME = "iframe",
    KEYGEN = "keygen",
    LABEL = "label",
    LI = "li",
    LINK = "link",
    LISTING = "listing",
    MAIN = "main",
    MALIGNMARK = "malignmark",
    MARQUEE = "marquee",
    MATH = "math",
    MENU = "menu",
    META = "meta",
    MGLYPH = "mglyph",
    MI = "mi",
    MO = "mo",
    MN = "mn",
    MS = "ms",
    MTEXT = "mtext",
    NAV = "nav",
    NOBR = "nobr",
    NOFRAMES = "noframes",
    NOEMBED = "noembed",
    NOSCRIPT = "noscript",
    OBJECT = "object",
    OL = "ol",
    OPTGROUP = "optgroup",
    OPTION = "option",
    P = "p",
    PARAM = "param",
    PLAINTEXT = "plaintext",
    PRE = "pre",
    RB = "rb",
    RP = "rp",
    RT = "rt",
    RTC = "rtc",
    RUBY = "ruby",
    S = "s",
    SCRIPT = "script",
    SEARCH = "search",
    SECTION = "section",
    SELECT = "select",
    SOURCE = "source",
    SMALL = "small",
    SPAN = "span",
    STRIKE = "strike",
    STRONG = "strong",
    STYLE = "style",
    SUB = "sub",
    SUMMARY = "summary",
    SUP = "sup",
    TABLE = "table",
    TBODY = "tbody",
    TEMPLATE = "template",
    TEXTAREA = "textarea",
    TFOOT = "tfoot",
    TD = "td",
    TH = "th",
    THEAD = "thead",
    TITLE = "title",
    TR = "tr",
    TRACK = "track",
    TT = "tt",
    U = "u",
    UL = "ul",
    SVG = "svg",
    VAR = "var",
    WBR = "wbr",
    XMP = "xmp"
}
/**
 * Tag IDs are numeric IDs for known tag names.
 *
 * We use tag IDs to improve the performance of tag name comparisons.
 */
export declare enum TAG_ID {
    UNKNOWN = 0,
    A = 1,
    ADDRESS = 2,
    ANNOTATION_XML = 3,
    APPLET = 4,
    AREA = 5,
    ARTICLE = 6,
    ASIDE = 7,
    B = 8,
    BASE = 9,
    BASEFONT = 10,
    BGSOUND = 11,
    BIG = 12,
    BLOCKQUOTE = 13,
    BODY = 14,
    BR = 15,
    BUTTON = 16,
    CAPTION = 17,
    CENTER = 18,
    CODE = 19,
    COL = 20,
    COLGROUP = 21,
    DD = 22,
    DESC = 23,
    DETAILS = 24,
    DIALOG = 25,
    DIR = 26,
    DIV = 27,
    DL = 28,
    DT = 29,
    EM = 30,
    EMBED = 31,
    FIELDSET = 32,
    FIGCAPTION = 33,
    FIGURE = 34,
    FONT = 35,
    FOOTER = 36,
    FOREIGN_OBJECT = 37,
    FORM = 38,
    FRAME = 39,
    FRAMESET = 40,
    H1 = 41,
    H2 = 42,
    H3 = 43,
    H4 = 44,
    H5 = 45,
    H6 = 46,
    HEAD = 47,
    HEADER = 48,
    HGROUP = 49,
    HR = 50,
    HTML = 51,
    I = 52,
    IMG = 53,
    IMAGE = 54,
    INPUT = 55,
    IFRAME = 56,
    KEYGEN = 57,
    LABEL = 58,
    LI = 59,
    LINK = 60,
    LISTING = 61,
    MAIN = 62,
    MALIGNMARK = 63,
    MARQUEE = 64,
    MATH = 65,
    MENU = 66,
    META = 67,
    MGLYPH = 68,
    MI = 69,
    MO = 70,
    MN = 71,
    MS = 72,
    MTEXT = 73,
    NAV = 74,
    NOBR = 75,
    NOFRAMES = 76,
    NOEMBED = 77,
    NOSCRIPT = 78,
    OBJECT = 79,
    OL = 80,
    OPTGROUP = 81,
    OPTION = 82,
    P = 83,
    PARAM = 84,
    PLAINTEXT = 85,
    PRE = 86,
    RB = 87,
    RP = 88,
    RT = 89,
    RTC = 90,
    RUBY = 91,
    S = 92,
    SCRIPT = 93,
    SEARCH = 94,
    SECTION = 95,
    SELECT = 96,
    SOURCE = 97,
    SMALL = 98,
    SPAN = 99,
    STRIKE = 100,
    STRONG = 101,
    STYLE = 102,
    SUB = 103,
    SUMMARY = 104,
    SUP = 105,
    TABLE = 106,
    TBODY = 107,
    TEMPLATE = 108,
    TEXTAREA = 109,
    TFOOT = 110,
    TD = 111,
    TH = 112,
    THEAD = 113,
    TITLE = 114,
    TR = 115,
    TRACK = 116,
    TT = 117,
    U = 118,
    UL = 119,
    SVG = 120,
    VAR = 121,
    WBR = 122,
    XMP = 123
}
export declare function getTagID(tagName: string): TAG_ID;
export declare const SPECIAL_ELEMENTS: Record<NS, Set<TAG_ID>>;
export declare const NUMBERED_HEADERS: Set<TAG_ID>;
export declare function hasUnescapedText(tn: string, scriptingEnabled: boolean): boolean;
