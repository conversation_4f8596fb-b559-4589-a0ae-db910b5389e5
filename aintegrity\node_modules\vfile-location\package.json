{"name": "vfile-location", "version": "5.0.3", "description": "vfile utility to convert between positional (line and column-based) and offset (range-based) locations", "license": "MIT", "keywords": ["vfile", "vfile-util", "util", "utility", "virtual", "file", "location", "point", "position", "offset"], "repository": "vfile/vfile-location", "bugs": "https://github.com/vfile/vfile-location/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)", "<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "files": ["lib/", "index.d.ts", "index.js"], "dependencies": {"@types/unist": "^3.0.0", "vfile": "^6.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^10.0.0", "prettier": "^3.0.0", "remark-api": "^1.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^10.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.58.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --log-level warn && xo --fix", "test-api": "node --conditions development test.js", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm", "remark-api"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true, "rules": {"no-constant-condition": "off", "unicorn/prefer-at": "off"}}}