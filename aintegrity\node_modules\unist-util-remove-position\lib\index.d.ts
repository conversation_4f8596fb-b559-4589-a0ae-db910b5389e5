/**
 * Remove the `position` field from a tree.
 *
 * @param {Node} tree
 *   Tree to clean.
 * @param {Options | null | undefined} [options={force: false}]
 *   Configuration (default: `{force: false}`).
 * @returns {undefined}
 *   Nothing.
 */
export function removePosition(
  tree: Node,
  options?: Options | null | undefined
): undefined
export type Node = import('unist').Node
/**
 * Configuration.
 */
export type Options = {
  /**
   * Whether to use `delete` to remove `position` fields.
   *
   * The default is to set them to `undefined`.
   */
  force?: boolean | null | undefined
}
