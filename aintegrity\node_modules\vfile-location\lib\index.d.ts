/**
 * @import {VFile, Value} from 'vfile'
 * @import {Location} from 'vfile-location'
 */
/**
 * Create an index of the given document to translate between line/column and
 * offset based positional info.
 *
 * Also implemented in Rust in [`wooorm/markdown-rs`][markdown-rs].
 *
 * [markdown-rs]: https://github.com/wooorm/markdown-rs/blob/main/src/util/location.rs
 *
 * @param {VFile | Value} file
 *   File to index.
 * @returns {Location}
 *   Accessors for index.
 */
export function location(file: VFile | Value): Location;
import type { VFile } from 'vfile';
import type { Value } from 'vfile';
import type { Location } from 'vfile-location';
//# sourceMappingURL=index.d.ts.map